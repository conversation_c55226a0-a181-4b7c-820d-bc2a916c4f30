<template>
  <div class="main">
    <SearchForm v-model:form="formArr" :page-type="PageType.BRAND_MANAGEMENT" @search="search" @setting="tableRef?.showTableSetting()" />
    <!-- 表格 -->
    <BaseTable
      ref="tableRef"
      v-model:form="formArr"
      :page-type="PageType.BRAND_MANAGEMENT"
      :get-list="GetList"
      :form-format="formFormat"
      :data-format="dataFormat"
      :is-checkbox="true"
      :check-cb="handleSelectionChange"
      :checkbox-config="{
        reserve: true,
        checkMethod: ({ row }) => {
          // 如果行有auth_status字段且值为2，或者mgmt_status字段且值为0，则禁用选择
          return !(row.auth_status === 2 || row.mgmt_status === 0)
        },
      }"
    >
      <template #left-btn>
        <a-button :disabled="!btnPermission[810001]" type="primary" @click="tapManipulate('add')">新建品牌</a-button>
      </template>

      <template #right-btn>
        <a-button :disabled="!btnPermission[810004] || selectedItems.length === 0" @click="batchToggleStatus('启用')" class="btn">批量启用</a-button>
        <a-button :disabled="!btnPermission[810004] || selectedItems.length === 0" @click="batchToggleStatus('禁用')" class="btn" danger>批量停用</a-button>
      </template>
      <template #logo_id="{ row }">
        <div class="logo-container">
          <img
            v-if="row.logo_id && logoUrls[row.logo_id]"
            :src="logoUrls[row.logo_id]"
            alt="品牌LOGO"
            class="brand-logo clickable-logo"
            @click="previewLogo(row.logo_id)"
            title="点击预览"
            @error="handleImageError(row.logo_id)"
          />
          <div v-else-if="row.logo_id && !logoUrls[row.logo_id]" class="logo-loading">
            <loading-outlined />
          </div>
          <span v-else>--</span>
        </div>
      </template>

      <template #auth_file_original_name="{ row }">
        <span v-if="row.auth_file_id && row.auth_file_original_name" class="auth-file-link" @click="previewAuthFile(row.auth_file_id)" :title="`点击预览: ${row.auth_file_original_name}`">
          {{ row.auth_file_original_name }}
        </span>
        <span v-else>--</span>
      </template>

      <template #auth_file_en_original_name="{ row }">
        <span v-if="row.auth_file_id_en && row.auth_file_en_original_name" class="auth-file-link" @click="previewAuthFile(row.auth_file_id_en)" :title="`点击预览: ${row.auth_file_en_original_name}`">
          {{ row.auth_file_en_original_name }}
        </span>
        <span v-else>--</span>
      </template>

      <template #auth_period="{ row }">
        <span v-if="row.auth_start_at && row.auth_end_at">{{ formatDateOnly(row.auth_start_at) }} 至 {{ formatDateOnly(row.auth_end_at) }}</span>
        <span v-else-if="row.auth_end_at">
          {{ formatDateOnly(row.auth_end_at) }}
        </span>
        <span v-else>--</span>
      </template>
      <template #auth_status="{ row }">
        <a-tag v-if="row.auth_status === 1" color="green">正常</a-tag>
        <a-tag v-else-if="row.auth_status === 2" color="red">已过期</a-tag>
        <a-tag v-else color="default">{{ row.auth_status || '--' }}</a-tag>
      </template>

      <template #auth_version="{ row }">
        <div class="version-column" @click="showVersionHistory(row)">
          <span>{{ row.auth_version || '--' }}</span>
        </div>
      </template>
      <template #status="{ row }">
        <!-- 当系统停用时(mgmt_status=0)，优先显示系统停用提示 -->
        <a-tooltip v-if="row.auth_status === 2" placement="top">
          <template #title>当前品牌 {{ row.brand_name }} 授权已过期，您无法进行启用操作。</template>
          <a-switch :disabled="true" class="btn disabled-switch" :checked="row.status === 1 || row.status === '启用'">
            <template #checkedChildren><check-outlined /></template>
            <template #unCheckedChildren><close-outlined /></template>
          </a-switch>
        </a-tooltip>
        <!-- 当授权过期时(auth_status=2)，禁用状态切换 -->
        <a-tooltip v-else-if="row.mgmt_status === 0" placement="top">
          <template #title>当前品牌 {{ row.brand_name }} 已被系统停用，您无法进行启用操作。</template>
          <a-switch :disabled="true" class="btn disabled-switch" :checked="row.status === 1 || row.status === '启用'">
            <template #checkedChildren><check-outlined /></template>
            <template #unCheckedChildren><close-outlined /></template>
          </a-switch>
        </a-tooltip>
        <!-- 当授权正常时(auth_status=1)，允许状态切换 -->
        <a-switch v-else :disabled="!btnPermission[810004]" class="btn" :checked="row.status === 1 || row.status === '启用'" @click="() => tapSwitch(row)">
          <template #checkedChildren><check-outlined /></template>
          <template #unCheckedChildren><close-outlined /></template>
        </a-switch>
      </template>

      <template #operate="{ row }">
        <a-button type="text" v-if="btnPermission[810005]" @click="detail(row)">查看</a-button>
        <a-button type="text" v-if="btnPermission[810002]" @click="tapManipulate('edit', row)">编辑</a-button>
        <a-button type="text" v-if="btnPermission[810003]" @click="deleteRole(row)" danger>删除</a-button>
      </template>
    </BaseTable>

    <!-- 新建/编辑抽屉 -->
    <EditDrawer ref="editDrawerRef" @save-success="saveSuccess" />

    <!-- 详情抽屉 -->
    <DetailDrawer ref="detailDrawerRef" />

    <!-- 确认对话框 -->
    <a-modal
      v-model:open="visibleData.isShow"
      :title="visibleData.title"
      :ok-text="visibleData.confirmBtnText"
      :ok-type="visibleData.okType"
      cancel-text="取消"
      @ok="visibleData.okFn"
      @cancel="visibleData.isShow = false"
      centered
    >
      <div style="white-space: pre-line">{{ visibleData.content }}</div>
    </a-modal>

    <!-- 图片预览组件 -->
    <a-image
      :width="0"
      :style="{ display: 'none' }"
      :preview="{
        visible: imageVisible,
        onVisibleChange: setImageVisible,
      }"
      :src="previewImageUrl"
    />

    <!-- 版本历史弹窗 -->
    <BrandVersionModal ref="versionRef" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { LoadingOutlined, CheckOutlined, CloseOutlined } from '@ant-design/icons-vue'
import BaseTable from '@/components/BaseTable/index.vue'
import SearchForm from '@/components/SearchForm/index.vue'
import dayjs from 'dayjs'

import { PageType } from '@/common/enum'
import { usePermission } from '@/hook/usePermission'
import { GetList, UpdateStatus, Delete } from '@/servers/Brand'
import { buttonDebounce } from '@/utils/index'
import useCommonOptionStore from '@/store/modules/commonOption'
import BrandVersionModal from './components/BrandVersionModal.vue'
import EditDrawer from './components/EditDrawer.vue'
import DetailDrawer from './components/DetailDrawer.vue'
// 获取环境变量和用户数据
const VITE_APP_ENV = ref(import.meta.env.VITE_APP_ENV)
const userData = ref(JSON.parse(localStorage.getItem('userData') || '{}'))
const commonOptionStore = useCommonOptionStore()
const { btnPermission } = usePermission()

// LOGO URL缓存
const logoUrls = ref<Record<number, string>>({})

// 表格和表单引用
const tableRef = ref()
const editDrawerRef = ref()
const detailDrawerRef = ref()
const versionRef = useTemplateRef<InstanceType<typeof BrandVersionModal>>('versionRef')

// 选中的项目
const selectedItems = ref<any[]>([])

// 图片预览相关
const imageVisible = ref(false)
const previewImageUrl = ref('')

// 搜索表单配置
const formArr: any = ref([
  {
    label: '搜索品牌编码',
    value: '',
    width: 300,
    type: 'batch-input',
    key: 'brand_number',
    // placeholder: '支持多编码输入，逗号隔开',
  },
  {
    label: '搜索品牌名称',
    value: null,
    type: 'input',
    key: 'brand_name',
  },
  {
    label: '默认制造商',
    value: null,
    type: 'input',
    key: 'manufacturer_name',
  },
  // {
  //   label: '所属供应商编码',
  //   value: null,
  //   type: 'input',
  //   key: 'supplierid',

  // },
  // {
  //   label: '所属供应商名称',
  //   value: null,
  //   type: 'input',
  //   key: 'supplier_name',
  // },
  {
    label: '授权书状态',
    value: null,
    type: 'select',
    options: [
      { label: '正常', value: '1' },
      { label: '已过期', value: '2' },
      // { label: '即将过期', value: '即将过期' },
    ],
    key: 'brand_file_status',
  },
  {
    label: '状态',
    value: null,
    type: 'select',
    options: [
      { label: '启用', value: '1' },
      { label: '停用', value: '0' },
    ],
    key: 'status',
  },
  {
    label: '创建时间',
    value: null,
    type: 'range-picker',
    key: 'create_at',
    formKeys: ['create_start_at', 'create_end_at'],
    placeholder: ['创建开始日期', '创建结束日期'],
    valueFormat: 'YYYY-MM-DD',
  },
  {
    label: '最后修改时间',
    value: null,
    type: 'range-picker',
    key: 'modified_at',
    formKeys: ['modified_start_at', 'modified_end_at'],
    placeholder: ['修改开始日期', '修改结束日期'],
    valueFormat: 'YYYY-MM-DD',
  },
])

// 确认对话框配置
const visibleData = reactive({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  okType: 'primary',
  content: '',
  okFn: () => {
    visibleData.isShow = false
  },
})

// 搜索功能
const search = () => tableRef.value.search()

// 处理选择变化
const handleSelectionChange = (items: any[]) => {
  console.log('BaseTable选择变化:', items)
  selectedItems.value = items
}

// 批量状态切换
const batchToggleStatus = (status: string) => {
  if (selectedItems.value.length === 0) {
    message.warning('请先选择要操作的品牌')
    return
  }

  const statusText = status === '启用' ? '启用' : '停用'
  const ids = selectedItems.value.map((item) => item.id)

  visibleData.title = `批量${statusText}品牌`
  visibleData.content = `即将批量${statusText} ${selectedItems.value.length} 个品牌，${statusText}后：`

  if (status === '启用') {
    visibleData.content += `
• 新的商品、商品类目等数据将无法关联该品牌，
• 已关联此品牌的商品、商品类目等，数据将被保留并可以正常使用。`
  } else {
    visibleData.content += `
• 新的商品、商品类目等数据将无法关联该品牌，
• 已关联此品牌的商品、商品类目等，数据将被保留并可以正常使用。`
  }

  visibleData.content += `\n\n此操作不可撤销，确定要批量${statusText}这些品牌吗？`
  visibleData.confirmBtnText = `批量${statusText}`
  visibleData.okType = status === '禁用' ? 'danger' : 'primary'
  visibleData.okFn = () => confirmBatchToggleStatus(ids, status)
  visibleData.isShow = true
}

// 确认批量状态切换
const confirmBatchToggleStatus = (ids: number[], status: string) => {
  const requestData = {
    ids,
    status: status === '启用' ? '1' : '0', // 使用数字字符串：1启用，0禁用
  }

  console.log('批量状态切换接口调用:', requestData)

  UpdateStatus(requestData)
    .then(() => {
      message.success(`批量${status}成功`)
      visibleData.isShow = false
      selectedItems.value = []
      tableRef.value?.clearCheckbox()
      search()
      commonOptionStore.updateCommonOption([15, 19])
    })
    .catch((error) => {
      console.error('批量状态切换失败:', error)

      visibleData.isShow = false
    })
}

// 新建/编辑操作
const tapManipulate = (type: string, row?: any) => {
  editDrawerRef.value?.open(type, row)
}

// 查看详情
const detail = (row: any) => {
  detailDrawerRef.value?.open(row.id, btnPermission.value[810005])
}

// 保存成功回调
const saveSuccess = () => {
  search()
}

// 状态切换
const oldStatus = ref(null)
const tapSwitch = buttonDebounce((row: any) => {
  // 保存原始状态，用于取消时恢复
  oldStatus.value = row.status

  // 根据当前状态确定要执行的操作
  const statusAction = row.status === 1 ? '停用' : '启用'

  visibleData.title = `${statusAction}品牌`
  visibleData.content = `即将${statusAction}品牌"${row.brand_name}"，${statusAction}后：`

  if (statusAction === '启用') {
    visibleData.content += `
• 新的商品、商品类目等数据可以关联该品牌。
• 已关联此品牌的商品、商品类目等，数据将继续保留并可正常使用。`
  } else {
    visibleData.content += `
• 新的商品、商品类目等数据将无法关联该品牌。
• 已关联此品牌的商品、商品类目等，数据将被保留并可以正常使用。
`
  }

  visibleData.content += `\n\n确定要${statusAction}品牌吗？`
  visibleData.confirmBtnText = statusAction
  visibleData.okType = statusAction === '停用' ? 'danger' : 'primary'
  visibleData.okFn = () => updateBrandStatus(row, statusAction)
  visibleData.isShow = true
}, 300)

// 更新品牌状态
const updateBrandStatus = (row: any, statusAction: string) => {
  // 调用 ToggleStatus 接口，传递正确的参数格式
  const requestData = {
    ids: [row.id],
    status: statusAction === '启用' ? '1' : '0', // 使用数字字符串：1启用，0禁用
  }

  console.log('调用状态切换接口:', requestData)

  UpdateStatus(requestData)
    .then(() => {
      // message.success(`${statusAction}成功`)
      visibleData.isShow = false
      // 刷新表格数据
      commonOptionStore.updateCommonOption([15, 19])
      search()
    })
    .catch((error) => {
      console.error('更新品牌状态失败:', error)
      // message.error(`${statusAction}失败`)
      visibleData.isShow = false
    })
}

// 删除品牌
const deleteRole = (row: any) => {
  visibleData.title = '删除品牌'
  visibleData.content = `即将删除品牌"${row.brand_name}" ，删除后：
  • 现有系统中，商品和商品类目等数据将无法关联此品牌。
• 请在执行此操作前确认：
• 不能使用“停用”操作来限制该品牌的使用，必须被删除。
• 该品牌已无需再被使用。

此操作不可恢复，确定要删除该品牌吗？
  `
  visibleData.confirmBtnText = '删除'
  visibleData.okType = 'danger'
  visibleData.okFn = () => confirmDelete(row.id)
  visibleData.isShow = true
}

// 确认删除
const confirmDelete = (id: string) => {
  Delete({ id })
    .then(() => {
      message.success('删除成功')
      visibleData.isShow = false
      search()
      commonOptionStore.updateCommonOption([15, 19])
    })
    .catch((error) => {
      console.error('删除品牌失败:', error)
      // message.error('删除失败')
      visibleData.isShow = false
    })
}

// 表单数据格式化函数
const formFormat = (obj: any) => {
  // 处理日期字段，确保格式正确
  const formatDate = (dateStr: string) => {
    if (!dateStr) return null
    // 如果已经是ISO格式，直接返回
    if (dateStr.includes('T')) return dateStr
    // 否则转换为ISO格式，添加时间部分
    const date = dayjs(dateStr)
    return date.format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
  }

  // 格式化日期字段（这些字段由range-picker的formKeys生成）
  if (obj.create_start_at) {
    obj.create_start_at = formatDate(obj.create_start_at)
  }
  if (obj.create_end_at) {
    obj.create_end_at = formatDate(obj.create_end_at)
  }
  if (obj.modified_start_at) {
    obj.modified_start_at = formatDate(obj.modified_start_at)
  }
  if (obj.modified_end_at) {
    obj.modified_end_at = formatDate(obj.modified_end_at)
  }

  // 根据API文档要求，构建完整的参数结构
  const formattedParams = {
    // 分页参数 - 修复参数名称映射
    is_page: true,
    page: obj.page || 1,
    pageSize: obj.pageSize || 20, // page_size 改成pageSize
    is_get_total: true,
    is_get_total_only: false,

    // 排序参数 - 修复排序参数格式
    sortField: obj.sortField || null,
    sortType: obj.sortType === 'true' ? 'asc' : obj.sortType === 'false' ? 'desc' : null,

    // 筛选和搜索参数
    filter_items: [], // 筛选项数组（暂时为空，为将来扩展预留）
    keyword: null, // 关键词搜索
    is_include_deleted: false, // 不包含已删除数据
    is_inner: true, // 内部数据

    // 业务字段映射
    brand_number: obj.brand_number || null,
    brand_name: obj.brand_name || null,
    manufacturer_name: obj.manufacturer_name || null,
    brand_file_status: obj.brand_file_status || null,
    status: obj.status || null,
    create_start_at: obj.create_start_at || null,
    create_end_at: obj.create_end_at || null,
    modified_start_at: obj.modified_start_at || null,
    modified_end_at: obj.modified_end_at || null,
    supplier_name: obj.supplier_name || null,
    supplierid: obj.supplierid || null,
  }

  return formattedParams
}

// 格式化日期，只显示年月日
const formatDateOnly = (dateStr: string) => {
  if (!dateStr) return '--'
  return dayjs(dateStr).format('YYYY-MM-DD')
}

// 数据格式化函数
const dataFormat = (data: any) => {
  console.log('BaseTable传入的数据:', data)
  // BaseTable组件会从API响应中提取 res.data.data 或 res.data.list
  // 但我们的API返回的数据直接在 res.data 中，所以这里的data就是我们需要的数组
  if (Array.isArray(data)) {
    // 异步加载LOGO
    loadLogos(data)
    return data
  }
  // 如果不是数组，返回空数组
  return []
}

// 图片预览控制
const setImageVisible = (visible: boolean) => {
  imageVisible.value = visible
}

// LOGO预览功能
const previewLogo = async (logoId: number) => {
  if (!logoId) {
    message.warning('LOGO文件不存在')
    return
  }

  try {
    let url = ''
    if (VITE_APP_ENV.value == 'development') {
      url = `${window.location.origin}/api/api/Files/ViewByFileId?fileId=${logoId}`
    } else {
      url = `${window.location.origin}/api/Files/ViewByFileId?fileId=${logoId}`
    }

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/octet-stream',
        logintoken: userData.value.login_token,
      },
    })

    if (!response.ok) {
      message.warning('获取LOGO失败')
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const blob = await response.blob()
    const previewUrl = URL.createObjectURL(blob)

    // 检查文件类型，如果是图片则使用图片预览组件，否则在新窗口打开
    const contentType = response.headers.get('content-type') || ''
    if (contentType.startsWith('image/')) {
      previewImageUrl.value = previewUrl
      imageVisible.value = true
      // 30秒后释放内存
      setTimeout(() => URL.revokeObjectURL(previewUrl), 30000)
    } else {
      // 非图片文件在新窗口打开
      window.open(previewUrl, '_blank')
      setTimeout(() => URL.revokeObjectURL(previewUrl), 30000)
    }
  } catch (error) {
    console.error('LOGO预览失败:', error)
    // message.error('LOGO预览失败')
  }
}

// 授权书预览功能
const previewAuthFile = async (fileId: number) => {
  if (!fileId) {
    message.warning('授权书文件不存在')
    return
  }

  try {
    let url = ''
    if (VITE_APP_ENV.value == 'development') {
      url = `${window.location.origin}/api/api/Files/ViewByFileId?fileId=${fileId}`
    } else {
      url = `${window.location.origin}/api/Files/ViewByFileId?fileId=${fileId}`
    }

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/octet-stream',
        logintoken: userData.value.login_token,
      },
    })

    if (!response.ok) {
      message.warning('获取授权书失败')
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const blob = await response.blob()
    const previewUrl = URL.createObjectURL(blob)

    // 检查文件类型
    const contentType = response.headers.get('content-type') || ''
    if (contentType.startsWith('image/')) {
      // 图片文件使用图片预览组件
      previewImageUrl.value = previewUrl
      imageVisible.value = true
      setTimeout(() => URL.revokeObjectURL(previewUrl), 30000)
    } else {
      // PDF等其他文件在新窗口打开
      window.open(previewUrl, '_blank')
      setTimeout(() => URL.revokeObjectURL(previewUrl), 30000)
    }
  } catch (error) {
    console.error('授权书预览失败:', error)
    // message.error('授权书预览失败')
  }
}

// 生成LOGO URL
const generateLogoUrl = async (logoId: number): Promise<string> => {
  try {
    let url = ''
    if (VITE_APP_ENV.value == 'development') {
      url = `${window.location.origin}/api/api/Files/ViewByFileId?fileId=${logoId}`
    } else {
      url = `${window.location.origin}/api/Files/ViewByFileId?fileId=${logoId}`
    }

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/octet-stream',
        logintoken: userData.value.login_token,
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const blob = await response.blob()
    return URL.createObjectURL(blob)
  } catch (error) {
    console.error('生成LOGO URL失败:', error)
    return ''
  }
}

// 处理图片加载错误
const handleImageError = (logoId: number) => {
  console.error(`LOGO图片加载失败: ${logoId}`)
  // 从缓存中移除失败的URL
  if (logoUrls.value[logoId]) {
    URL.revokeObjectURL(logoUrls.value[logoId])
    delete logoUrls.value[logoId]
  }
}

// 加载所有LOGO
const loadLogos = async (data: any[]) => {
  const logoIds = data.filter((item) => item.logo_id && !logoUrls.value[item.logo_id]).map((item) => item.logo_id)

  // 并发加载所有LOGO
  const promises = logoIds.map(async (logoId) => {
    const url = await generateLogoUrl(logoId)
    if (url) {
      logoUrls.value[logoId] = url
    }
  })

  await Promise.all(promises)
}

// 显示版本历史
const showVersionHistory = (row: any) => {
  if (!row.id) {
    message.warning('品牌ID不存在')
    return
  }
  versionRef.value?.show(row.id)
}

onMounted(() => {
  // 页面加载时直接搜索数据
  search()
})
</script>

<style scoped>
.version-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: underline;

  &:hover {
    cursor: pointer;
  }
}

.version-divider {
  padding: 2px 8px;
  margin-top: 4px;
  font-size: 12px;
  color: #1890ff;
  cursor: pointer;
  border-top: 1px solid #d9d9d9;
  transition: all 0.3s;
}

.version-divider:hover {
  color: #40a9ff;
  background-color: #f0f8ff;
}
</style>

<style lang="scss" scoped>
.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  color: #1890ff;
}

.brand-logo {
  width: 40px;
  height: 40px;
  object-fit: contain;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.clickable-logo {
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.auth-file-link {
  color: #1890ff;
  text-decoration: underline;
  cursor: pointer;
  transition: color 0.3s ease;

  &:hover {
    color: #40a9ff;
    text-decoration: underline;
  }

  &:active {
    color: #096dd9;
  }
}

.disabled-switch {
  cursor: not-allowed;
  opacity: 0.5;
}

.disabled-btn {
  cursor: not-allowed;
  opacity: 0.5;
}

// 震动动画
@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-2px);
  }

  20%,
  40%,
  60%,
  80% {
    transform: translateX(2px);
  }
}
</style>
